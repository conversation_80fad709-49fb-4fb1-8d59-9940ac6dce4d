"""
Comprehensive test suite for the job API
"""

import pytest
import asyncio
import json
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch, AsyncMock
import httpx

# Import the main app
from main import app
from config import get_settings
from cache import cache
from supabase_client import db_manager

# Test client
client = TestClient(app)

# Test configuration
settings = get_settings()


class TestHealthChecks:
    """Test health check endpoints"""
    
    def test_basic_health_check(self):
        """Test basic health endpoint"""
        response = client.get("/health/")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert data["service"] == "jobbify-api"
    
    def test_liveness_check(self):
        """Test liveness probe endpoint"""
        response = client.get("/health/liveness")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "alive"
        assert "uptime_seconds" in data
    
    @patch('health_checks.check_database_health')
    def test_readiness_check_healthy(self, mock_db_health):
        """Test readiness check when database is healthy"""
        mock_db_health.return_value = {"status": "healthy"}
        
        response = client.get("/health/readiness")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "ready"
    
    @patch('health_checks.check_database_health')
    def test_readiness_check_unhealthy(self, mock_db_health):
        """Test readiness check when database is unhealthy"""
        mock_db_health.return_value = {"status": "unhealthy", "error": "Connection failed"}
        
        response = client.get("/health/readiness")
        assert response.status_code == 503
        data = response.json()
        assert data["status"] == "not_ready"


class TestJobEndpoints:
    """Test job-related endpoints"""
    
    def test_fetch_jobs_endpoint(self):
        """Test basic job fetching"""
        with patch('main.supabase') as mock_supabase:
            mock_supabase.table.return_value.select.return_value.limit.return_value.execute.return_value.data = [
                {
                    "id": "test-job-1",
                    "title": "Software Engineer",
                    "company": "Test Corp",
                    "location": "Remote"
                }
            ]
            
            response = client.get("/jobs/?limit=10")
            assert response.status_code == 200
            data = response.json()
            assert len(data) == 1
            assert data[0]["title"] == "Software Engineer"
    
    def test_job_search_validation(self):
        """Test job search input validation"""
        # Test missing required fields
        response = client.post("/jobs/search", json={})
        assert response.status_code == 422
        
        # Test invalid salary range
        response = client.post("/jobs/search", json={
            "keywords": "python",
            "location": "San Francisco",
            "min_salary": 100000,
            "max_salary": 50000  # max < min
        })
        assert response.status_code == 422
    
    @patch('main.jooble')
    @patch('main.muse')
    async def test_job_search_success(self, mock_muse, mock_jooble):
        """Test successful job search"""
        # Mock API responses
        mock_jooble.return_value = [{"title": "Python Developer", "company": "Tech Co"}]
        mock_muse.return_value = [{"title": "Software Engineer", "company": "Startup Inc"}]
        
        response = client.post("/jobs/search", json={
            "keywords": "python developer",
            "location": "San Francisco",
            "min_salary": 80000,
            "max_salary": 120000
        })
        
        assert response.status_code == 200
        data = response.json()
        assert "jobs" in data
        assert "total_found" in data


class TestRateLimiting:
    """Test rate limiting functionality"""
    
    def test_rate_limit_enforcement(self):
        """Test that rate limiting works"""
        # Make many requests quickly to trigger rate limit
        responses = []
        for i in range(70):  # Exceed the 60/minute limit
            response = client.get("/health/")
            responses.append(response.status_code)
        
        # Should have some 429 responses
        assert 429 in responses


class TestErrorHandling:
    """Test error handling and responses"""
    
    def test_404_endpoint(self):
        """Test non-existent endpoint"""
        response = client.get("/nonexistent")
        assert response.status_code == 404
    
    def test_validation_error_format(self):
        """Test validation error response format"""
        response = client.post("/jobs/search", json={"keywords": ""})  # Empty keywords
        assert response.status_code == 422
        data = response.json()
        assert "error" in data
        assert "validation_errors" in data["error"]["details"]


class TestCacheOperations:
    """Test caching functionality"""
    
    @pytest.mark.asyncio
    async def test_cache_set_get(self):
        """Test basic cache operations"""
        if not cache.connected:
            pytest.skip("Redis not available")
        
        test_key = "test_cache_key"
        test_data = {"test": "data", "timestamp": 123456}
        
        # Set data
        result = await cache.set(test_key, test_data, 60)
        assert result is True
        
        # Get data
        retrieved = await cache.get(test_key)
        assert retrieved is not None
        assert retrieved["data"]["test"] == "data"
    
    @pytest.mark.asyncio
    async def test_cache_expiration(self):
        """Test cache expiration"""
        if not cache.connected:
            pytest.skip("Redis not available")
        
        test_key = "test_expiry_key"
        test_data = {"test": "expiry"}
        
        # Set with very short TTL
        await cache.set(test_key, test_data, 1)
        
        # Should exist immediately
        retrieved = await cache.get(test_key)
        assert retrieved is not None
        
        # Wait for expiration
        await asyncio.sleep(2)
        
        # Should be expired
        retrieved = await cache.get(test_key)
        assert retrieved is None


class TestDatabaseOperations:
    """Test database operations"""
    
    @patch('supabase_client.supabase')
    def test_safe_select(self, mock_supabase):
        """Test safe database select operation"""
        mock_supabase.table.return_value.select.return_value.execute.return_value.data = [
            {"id": 1, "name": "test"}
        ]
        
        result = db_manager.safe_select("test_table", "id,name")
        assert result["error"] is None
        assert len(result["data"]) == 1
    
    @patch('supabase_client.supabase')
    def test_safe_insert(self, mock_supabase):
        """Test safe database insert operation"""
        mock_supabase.table.return_value.insert.return_value.execute.return_value.data = [
            {"id": 1, "name": "inserted"}
        ]
        
        result = db_manager.safe_insert("test_table", {"name": "test"})
        assert result["error"] is None
        assert result["data"] is not None


class TestUtilityFunctions:
    """Test utility functions"""
    
    def test_generate_cache_key(self):
        """Test cache key generation"""
        from utils import generate_cache_key
        
        key1 = generate_cache_key("location", ["python", "java"], "senior")
        key2 = generate_cache_key("location", ["java", "python"], "senior")  # Different order
        
        # Should be the same due to sorting
        assert key1 == key2
    
    def test_sanitize_string(self):
        """Test string sanitization"""
        from utils import sanitize_string
        
        # Test normal string
        result = sanitize_string("Hello World")
        assert result == "Hello World"
        
        # Test string with control characters
        result = sanitize_string("Hello\x00World\x01")
        assert result == "HelloWorld"
        
        # Test long string truncation
        long_string = "a" * 2000
        result = sanitize_string(long_string, max_length=100)
        assert len(result) <= 103  # 100 + "..."
    
    def test_extract_salary_range(self):
        """Test salary range extraction"""
        from utils import extract_salary_range
        
        # Test range format
        result = extract_salary_range("$80,000 - $120,000")
        assert result["min"] == 80000
        assert result["max"] == 120000
        
        # Test k format
        result = extract_salary_range("80k-120k")
        assert result["min"] == 80000
        assert result["max"] == 120000
        
        # Test single value
        result = extract_salary_range("100k")
        assert result["min"] == 100000
        assert result["max"] == 100000


# Pytest configuration
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
