import os
import logging
from supabase import create_client, Client
from dotenv import load_dotenv
import asyncio
from typing import Optional, Dict, Any, List
import time
from functools import wraps

load_dotenv()

logger = logging.getLogger(__name__)

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")

if not SUPABASE_URL or not SUPABASE_KEY:
    raise RuntimeError("Missing SUPABASE_URL or SUPABASE_KEY in .env")

# Connection configuration
CLIENT_OPTIONS = {
    "auto_refresh_token": True,
    "persist_session": True,
    "detect_session_in_url": False,
    "headers": {
        "User-Agent": "Jobbify-API/1.0"
    }
}

# Create Supabase client with optimized configuration
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY, options=CLIENT_OPTIONS)


class DatabaseManager:
    """Enhanced database manager with connection pooling and retry logic"""

    def __init__(self, client: Client):
        self.client = client
        self.connection_pool_size = int(os.getenv("DB_POOL_SIZE", "10"))
        self.retry_attempts = int(os.getenv("DB_RETRY_ATTEMPTS", "3"))
        self.retry_delay = float(os.getenv("DB_RETRY_DELAY", "1.0"))

    def retry_on_failure(self, max_retries: int = None):
        """Decorator for retrying database operations"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                retries = max_retries or self.retry_attempts
                last_exception = None

                for attempt in range(retries + 1):
                    try:
                        return await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
                    except Exception as e:
                        last_exception = e
                        if attempt < retries:
                            wait_time = self.retry_delay * (2 ** attempt)  # Exponential backoff
                            logger.warning(
                                f"Database operation failed (attempt {attempt + 1}/{retries + 1}), retrying in {wait_time}s",
                                extra={"exception": str(e), "function": func.__name__}
                            )
                            await asyncio.sleep(wait_time)
                        else:
                            logger.error(
                                f"Database operation failed after {retries + 1} attempts",
                                extra={"exception": str(e), "function": func.__name__}
                            )

                raise last_exception
            return wrapper
        return decorator

    @retry_on_failure()
    def safe_select(self, table: str, columns: str = "*", **filters) -> Dict[str, Any]:
        """Safe select operation with retry logic"""
        try:
            query = self.client.table(table).select(columns)

            # Apply filters
            for key, value in filters.items():
                if value is not None:
                    query = query.eq(key, value)

            result = query.execute()
            return {"data": result.data, "error": None}
        except Exception as e:
            logger.error(f"Select operation failed: {str(e)}", extra={"table": table, "filters": filters})
            return {"data": [], "error": str(e)}

    @retry_on_failure()
    def safe_insert(self, table: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Safe insert operation with retry logic"""
        try:
            result = self.client.table(table).insert(data).execute()
            return {"data": result.data, "error": None}
        except Exception as e:
            logger.error(f"Insert operation failed: {str(e)}", extra={"table": table, "data": data})
            return {"data": None, "error": str(e)}

    @retry_on_failure()
    def safe_update(self, table: str, data: Dict[str, Any], **filters) -> Dict[str, Any]:
        """Safe update operation with retry logic"""
        try:
            query = self.client.table(table).update(data)

            # Apply filters
            for key, value in filters.items():
                if value is not None:
                    query = query.eq(key, value)

            result = query.execute()
            return {"data": result.data, "error": None}
        except Exception as e:
            logger.error(f"Update operation failed: {str(e)}", extra={"table": table, "data": data, "filters": filters})
            return {"data": None, "error": str(e)}

    @retry_on_failure()
    def safe_upsert(self, table: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Safe upsert operation with retry logic"""
        try:
            result = self.client.table(table).upsert(data).execute()
            return {"data": result.data, "error": None}
        except Exception as e:
            logger.error(f"Upsert operation failed: {str(e)}", extra={"table": table, "data": data})
            return {"data": None, "error": str(e)}


# Create global database manager instance
db_manager = DatabaseManager(supabase)